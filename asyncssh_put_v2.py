import asyncio
import asyncssh
import os

async def upload_files(host: str, user: str, password: str, files: list, concurrency=5):
    """执行批量文件上传"""
    try:
        async with asyncssh.connect(
            host, port=22, username=user, password=password,
            known_hosts=None
        ) as conn:
            
            async with conn.start_sftp_client() as sftp:
                semaphore = asyncio.Semaphore(concurrency)
                
                async def upload_one(local, remote):
                    async with semaphore:
                        # 进度回调函数
                        def progress(src_path, dst_path, sent, total):
                            print(f"正在上传 {os.path.basename(local)}：{sent/total:.0%}", end='\r')
                        
                        print(f"开始上传 {os.path.basename(local)}")
                        await sftp.put(local, remote, progress_handler=progress)
                        print(f"完成！http://{host}/{os.path.basename(remote)}\n")
                        return remote
                
                # 创建并执行所有上传任务
                tasks = [
                    asyncio.create_task(upload_one(local, remote))
                    for local, remote in files
                ]
                return await asyncio.gather(*tasks)
    
    except Exception as e:
        print(f"错误：{str(e)}")
        raise

if __name__ == "__main__":
    # 配置区：可自由修改 ▼
    files_to_upload = [
        (r"D:\Program\Clash.Verge\Config\815.yaml", "/var/srv/live/www/815.yaml"),
        (r"D:\Program\Clash.Verge\Config\816.yaml", "/var/srv/live/www/816.yaml"),
        (r"D:\Program\Clash.Verge\Config\818.yaml", "/var/srv/live/www/818.yaml"),
    ]
    
    # 从环境变量读取或使用默认值
    server_config = {
        "host": os.getenv('SSH_HOST', '***********'),
        "user": os.getenv('SSH_USER', 'root'),
        "password": os.getenv('SSH_PASSWORD', '(6sGCM+40Lu6cz')
    }
    
    # 打印文件路径
    for local_file, _ in files_to_upload:
        print(f"检查文件: {local_file}, 存在: {os.path.exists(local_file)}")
    
    asyncio.run(upload_files(files=files_to_upload, **server_config))


