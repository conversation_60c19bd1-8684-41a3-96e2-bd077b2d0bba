# SSH文件传输工具集

## 项目介绍
基于 asyncssh 开发的高效 SSH 文件传输工具集，支持并发上传和可视化下载。

### 主要特点
- 异步 IO 技术，高效稳定
- 并发文件传输
- 实时进度可视化
- 适用于大规模运维场景

## 环境要求与安装
### 系统要求
- Python 3.7+
- 支持: Linux/Windows/MacOS

### 依赖安装
```bash
pip install asyncssh  # 异步SSH传输
pip install rich     # 终端美化
pip install asyncio  # 异步IO支持
```

## 技术架构
### 核心组件
1. **asyncssh**
   - 异步SSH协议支持
   - SFTP协议实现
   - 高效远程连接

2. **rich**
   - 终端美化
   - 实时进度展示
   - 传输速度监控

3. **asyncio**
   - 异步框架支持
   - 协程管理
   - 并发控制

## 功能特性

### 并发上传模块
- 多文件并发处理
- 自动结果反馈
- 灵活路径配置
- 异步效率优化

### 可视化下载模块
- 实时进度监控
- 智能文件管理
  - 自动重命名
  - 时间戳支持
- 传输状态追踪
  - 速度监控
  - 时间预估
  - 进度统计
- 大文件续传支持
