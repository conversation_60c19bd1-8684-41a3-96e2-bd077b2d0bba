# 更新：2024年11月9日11:37:31
import asyncio
import asyncssh
import os
from datetime import datetime
from rich.progress import Progress, TextColumn, BarColumn, TransferSpeedColumn, TimeRemainingColumn
from rich import print as rprint
import time

# 远程主机设置 ⭐️
hostname = "***********" # CLAW日本10
port = 22
username = "root"
password = "(6sGCM+40Lu6cz"

# 定义下载文件函数
async def download_file(local_file, remote_file, progress=None):
    async with asyncssh.connect(hostname, port=port, username=username, password=password, known_hosts=None) as conn:
        async with conn.start_sftp_client() as sftp:
            # 获取远程文件大小
            file_attrs = await sftp.stat(remote_file)
            total_size = file_attrs.size
            
            # 创建进度任务
            if progress:
                task_id = progress.add_task("[cyan]下载中...", total=total_size)
            
            # 修改：将进度回调改为普通函数而不是协程
            def progress_callback(src_path, dest_path, bytes_copied, total_bytes):
                if progress:
                    progress.update(task_id, completed=bytes_copied)
            
            # 开始下载
            await sftp.get(remote_file, local_file, progress_handler=progress_callback)

# 主函数
async def main():
    # 设置基本路径和文件名
    base_dir = r"D:\DeepResearch\240520_Docker安装Memos使用\memos_moodist241107\memos"  # 修改为绝对路径⭐️
    base_filename = "memos_prod.db"
    
    # 确保目录存在
    os.makedirs(base_dir, exist_ok=True)
    
    # 检查文件是否存在，如果存在则添加时间戳
    local_path = os.path.join(base_dir, base_filename)
    if os.path.exists(local_path):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"memos_prod_{timestamp}.db"
        local_path = os.path.join(base_dir, filename)
    
    remote_file = "/srv/memos_moodist241107/memos/memos_prod.db"
    
    # 记录开始时间
    start_time = time.time()
    start_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    rprint(f"[green]开始时间: {start_datetime}[/green]")
    rprint(f"[yellow]源文件: {remote_file}[/yellow]")
    rprint(f"[yellow]目标位置: {local_path}[/yellow]")
    
    # 创建进度条
    with Progress(
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        "[progress.percentage]{task.percentage:>3.0f}%",
        TransferSpeedColumn(),
        TimeRemainingColumn(),
    ) as progress:
        await download_file(local_path, remote_file, progress)
    
    # 计算用时
    end_time = time.time()
    end_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    duration = end_time - start_time
    
    # 获取文件大小
    file_size = os.path.getsize(local_path)
    file_size_mb = file_size / (1024 * 1024)  # 转换为MB
    
    # 打印完成信息
    rprint("\n[green]下载完成![/green]")
    rprint(f"[blue]结束时间: {end_datetime}[/blue]")
    rprint(f"[blue]总用时: {duration:.2f} 秒[/blue]")
    rprint(f"[blue]文件大小: {file_size_mb:.2f} MB[/blue]")

if __name__ == "__main__":
    asyncio.run(main())


