# 更新：2024年11月9日11:27:35
import asyncio
import asyncssh
from typing import List, Tuple

# 远程主机设置 ⭐️
hostname = "***********" # CLAW日本10
port = 22
username = "root"
password = "(6sGCM+40Lu6cz"

# 定义上传文件函数
async def up_file(conn: asyncssh.SSHClientConnection, local_file: str, remote_file: str, index: int):
    async with conn.start_sftp_client() as sftp:
        print(f"{index}开始上传...")
        await sftp.put(local_file, remote_file)
        print(f"上传完毕 http://{hostname}/{remote_file.split('/')[-1]}")

# 主函数
async def main():
    files_to_upload: List[Tuple[str, str]] = [
        (r"D:\Program\Clash.Verge\Config\815.yaml", "/var/srv/live/www/815.yaml"),
        (r"D:\Program\Clash.Verge\Config\816.yaml", "/var/srv/live/www/816.yaml"),
        (r"D:\Program\Clash.Verge\Config\818.yaml", "/var/srv/live/www/818.yaml"),
        
    ]

    async with asyncssh.connect(hostname, port=port, username=username, password=password, known_hosts=None) as conn:
        tasks = [up_file(conn, local_file, remote_file, index) for index, (local_file, remote_file) in enumerate(files_to_upload, start=1)]
        await asyncio.gather(*tasks)

asyncio.run(main())


